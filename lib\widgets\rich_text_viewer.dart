import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import '../models/rich_text_content.dart';

/// Widget for displaying rich text content in read-only mode
class RichTextViewer extends StatelessWidget {
  final RichTextContent? richTextContent;
  final String? plainText;
  final int? maxLines;
  final TextOverflow? overflow;
  final TextStyle? style;
  final bool showFormatting;

  const RichTextViewer({
    super.key,
    this.richTextContent,
    this.plainText,
    this.maxLines,
    this.overflow,
    this.style,
    this.showFormatting = true,
  });

  @override
  Widget build(BuildContext context) {
    // If no rich text content, show plain text
    if (richTextContent == null) {
      return Text(
        plainText ?? '',
        maxLines: maxLines,
        overflow: overflow ?? TextOverflow.ellipsis,
        style: style,
      );
    }

    // If rich text content exists but formatting is disabled, show plain text
    if (!showFormatting) {
      return Text(
        richTextContent!.plainText,
        maxLines: maxLines,
        overflow: overflow ?? TextOverflow.ellipsis,
        style: style,
      );
    }

    try {
      final document = richTextContent!.toDocument();
      final controller = QuillController(
        document: document,
        selection: const TextSelection.collapsed(offset: 0),
      );

      return ConstrainedBox(
        constraints: BoxConstraints(
          maxHeight: maxLines != null ? (maxLines! * 20.0) : double.infinity,
        ),
        child: QuillEditor.basic(
          controller: controller,
        ),
      );
    } catch (e) {
      // Fallback to plain text if rich text parsing fails
      return Text(
        richTextContent!.plainText,
        maxLines: maxLines,
        overflow: overflow ?? TextOverflow.ellipsis,
        style: style,
      );
    }
  }
}

/// Compact rich text viewer for note cards and previews
class CompactRichTextViewer extends StatelessWidget {
  final RichTextContent? richTextContent;
  final String? plainText;
  final int maxLines;
  final TextStyle? style;

  const CompactRichTextViewer({
    super.key,
    this.richTextContent,
    this.plainText,
    this.maxLines = 3,
    this.style,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Always show plain text for compact view to maintain consistency
    final text = richTextContent?.plainText ?? plainText ?? '';

    return Text(
      text,
      maxLines: maxLines,
      overflow: TextOverflow.ellipsis,
      style: style ??
          TextStyle(
            fontSize: 14,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
    );
  }
}

/// Rich text preview with formatting indicator
class RichTextPreview extends StatelessWidget {
  final RichTextContent? richTextContent;
  final String? plainText;
  final int maxLines;
  final TextStyle? style;
  final bool showFormattingIndicator;

  const RichTextPreview({
    super.key,
    this.richTextContent,
    this.plainText,
    this.maxLines = 6,
    this.style,
    this.showFormattingIndicator = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final hasFormatting =
        richTextContent?.getTextStatistics()['has_formatting'] == true;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Rich text content
        Expanded(
          child: CompactRichTextViewer(
            richTextContent: richTextContent,
            plainText: plainText,
            maxLines: maxLines,
            style: style,
          ),
        ),

        // Formatting indicator
        if (showFormattingIndicator && hasFormatting) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.format_paint,
                size: 12,
                color: theme.colorScheme.primary.withValues(alpha: 0.7),
              ),
              const SizedBox(width: 4),
              Text(
                'Rich text',
                style: TextStyle(
                  fontSize: 10,
                  color: theme.colorScheme.primary.withValues(alpha: 0.7),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }
}

/// Rich text statistics widget
class RichTextStats extends StatelessWidget {
  final RichTextContent richTextContent;
  final bool compact;

  const RichTextStats({
    super.key,
    required this.richTextContent,
    this.compact = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final stats = richTextContent.getTextStatistics();

    if (compact) {
      return Text(
        '${stats['word_count']} words',
        style: TextStyle(
          fontSize: 12,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
        ),
      );
    }

    return Wrap(
      spacing: 16,
      children: [
        _buildStatItem(
          context,
          Icons.text_fields,
          '${stats['word_count']} words',
        ),
        _buildStatItem(
          context,
          Icons.format_size,
          '${stats['character_count']} chars',
        ),
        if (stats['has_formatting'] == true)
          _buildStatItem(
            context,
            Icons.format_paint,
            'Formatted',
            color: theme.colorScheme.primary,
          ),
        if (stats['has_lists'] == true)
          _buildStatItem(
            context,
            Icons.format_list_bulleted,
            'Lists',
          ),
        if (stats['has_images'] == true)
          _buildStatItem(
            context,
            Icons.image,
            'Images',
          ),
      ],
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    IconData icon,
    String text, {
    Color? color,
  }) {
    final theme = Theme.of(context);
    final effectiveColor =
        color ?? theme.colorScheme.onSurface.withValues(alpha: 0.6);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 14,
          color: effectiveColor,
        ),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(
            fontSize: 12,
            color: effectiveColor,
          ),
        ),
      ],
    );
  }
}

/// Helper function to get rich text content from a note
RichTextContent? getRichTextContentFromNote(dynamic note) {
  if (note == null) return null;

  try {
    // Check if note has rich text content
    if (note.isRichText && note.richTextContent != null) {
      return RichTextContent(
        id: note.id,
        plainText: note.content,
        deltaJson: note.richTextContent,
        createdAt: note.createdAt,
        updatedAt: note.updatedAt,
      );
    }
  } catch (e) {
    // If there's an error, return null to fall back to plain text
  }

  return null;
}
