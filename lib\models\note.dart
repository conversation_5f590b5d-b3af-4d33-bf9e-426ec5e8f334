import 'base_model.dart';
import 'rich_text_content.dart';

class Note extends BaseModelWithTimestamps {
  final String id;
  final String title;
  final String content;
  final String category;
  final bool isFavorite;
  final bool isPinned;
  final List<String> tags;
  final String? richTextContent; // JSON string of Quill Delta
  final bool isRichText; // Flag to indicate if note uses rich text

  Note({
    required this.id,
    required this.title,
    required this.content,
    required this.category,
    this.isFavorite = false,
    this.isPinned = false,
    this.tags = const [],
    this.richTextContent,
    this.isRichText = false,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) : super(createdAt: createdAt, updatedAt: updatedAt);

  @override
  String get tableName => 'notes';

  @override
  String get primaryKey => 'id';

  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'category': category,
      'is_favorite': isFavorite ? 1 : 0,
      'is_pinned': isPinned ? 1 : 0,
      'tags': tags.join(','), // Store tags as comma-separated string
      'rich_text_content': richTextContent,
      'is_rich_text': isRichText ? 1 : 0,
      ...baseToMap(),
    };
  }

  factory Note.fromMap(Map<String, dynamic> map) {
    // Parse tags from comma-separated string
    final tagsString = map['tags'] as String? ?? '';
    final tags = tagsString.isEmpty
        ? <String>[]
        : tagsString
            .split(',')
            .map((tag) => tag.trim())
            .where((tag) => tag.isNotEmpty)
            .toList();

    return Note(
      id: map['id'],
      title: map['title'],
      content: map['content'],
      category: map['category'],
      isFavorite: map['is_favorite'] == 1,
      isPinned: map['is_pinned'] == 1,
      tags: tags,
      richTextContent: map['rich_text_content'],
      isRichText: map['is_rich_text'] == 1,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  Note copyWith({
    String? id,
    String? title,
    String? content,
    String? category,
    bool? isFavorite,
    bool? isPinned,
    List<String>? tags,
    String? richTextContent,
    bool? isRichText,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Note(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      category: category ?? this.category,
      isFavorite: isFavorite ?? this.isFavorite,
      isPinned: isPinned ?? this.isPinned,
      tags: tags ?? this.tags,
      richTextContent: richTextContent ?? this.richTextContent,
      isRichText: isRichText ?? this.isRichText,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Get rich text content as RichTextContent object
  RichTextContent? getRichTextContent() {
    if (!isRichText || richTextContent == null) return null;

    try {
      return RichTextContent(
        id: id,
        plainText: content,
        deltaJson: richTextContent!,
        createdAt: createdAt,
        updatedAt: updatedAt,
      );
    } catch (e) {
      return null;
    }
  }

  /// Create note from RichTextContent
  factory Note.fromRichTextContent({
    required String id,
    required String title,
    required RichTextContent richTextContent,
    required String category,
    bool isFavorite = false,
    bool isPinned = false,
    List<String> tags = const [],
  }) {
    return Note(
      id: id,
      title: title,
      content: richTextContent.plainText,
      category: category,
      isFavorite: isFavorite,
      isPinned: isPinned,
      tags: tags,
      richTextContent: richTextContent.deltaJson,
      isRichText: true,
      createdAt: richTextContent.createdAt,
      updatedAt: richTextContent.updatedAt,
    );
  }

  /// Update note with rich text content
  Note withRichTextContent(RichTextContent richTextContent) {
    return copyWith(
      content: richTextContent.plainText,
      richTextContent: richTextContent.deltaJson,
      isRichText: true,
      updatedAt: DateTime.now(),
    );
  }

  /// Convert to plain text note (removes rich text formatting)
  Note toPlainText() {
    return copyWith(
      richTextContent: null,
      isRichText: false,
      updatedAt: DateTime.now(),
    );
  }

  /// Get display content (plain text for compatibility)
  String get displayContent => content;

  /// Get word count from content
  int get wordCount {
    return content
        .split(RegExp(r'\s+'))
        .where((word) => word.isNotEmpty)
        .length;
  }

  /// Get character count from content
  int get characterCount => content.length;

  /// Check if note has any formatting
  bool get hasFormatting {
    return isRichText &&
        richTextContent != null &&
        richTextContent!.contains('"attributes"');
  }
}

class NoteAttachment extends BaseModel {
  final int? id;
  final String noteId;
  final String filePath;
  final String fileName;
  final String? fileType;
  final int? fileSize;
  final DateTime createdAt;

  NoteAttachment({
    this.id,
    required this.noteId,
    required this.filePath,
    required this.fileName,
    this.fileType,
    this.fileSize,
    required this.createdAt,
  });

  @override
  String get tableName => 'note_attachments';

  @override
  Map<String, dynamic> toMap() {
    final map = {
      'note_id': noteId,
      'file_path': filePath,
      'file_name': fileName,
      'file_type': fileType,
      'file_size': fileSize,
      'created_at': createdAt.toIso8601String(),
    };

    if (id != null) {
      map['id'] = id;
    }

    return map;
  }

  factory NoteAttachment.fromMap(Map<String, dynamic> map) {
    return NoteAttachment(
      id: map['id'],
      noteId: map['note_id'],
      filePath: map['file_path'],
      fileName: map['file_name'],
      fileType: map['file_type'],
      fileSize: map['file_size'],
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  NoteAttachment copyWith({
    int? id,
    String? noteId,
    String? filePath,
    String? fileName,
    String? fileType,
    int? fileSize,
    DateTime? createdAt,
  }) {
    return NoteAttachment(
      id: id ?? this.id,
      noteId: noteId ?? this.noteId,
      filePath: filePath ?? this.filePath,
      fileName: fileName ?? this.fileName,
      fileType: fileType ?? this.fileType,
      fileSize: fileSize ?? this.fileSize,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

class NoteTemplate {
  final String id;
  final String name;
  final String title;
  final String content;
  final String category;
  final String description;
  final List<String> tags;
  final bool isBuiltIn;
  final bool isFavorite;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? createdBy;
  final String? richTextContent; // JSON string of Quill Delta for templates
  final bool isRichText; // Flag to indicate if template uses rich text

  NoteTemplate({
    required this.id,
    required this.name,
    required this.title,
    required this.content,
    required this.category,
    this.description = '',
    this.tags = const [],
    this.isBuiltIn = false,
    this.isFavorite = false,
    required this.createdAt,
    required this.updatedAt,
    this.createdBy,
    this.richTextContent,
    this.isRichText = false,
  });

  /// Create a copy with updated values
  NoteTemplate copyWith({
    String? id,
    String? name,
    String? title,
    String? content,
    String? category,
    String? description,
    List<String>? tags,
    bool? isBuiltIn,
    bool? isFavorite,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? richTextContent,
    bool? isRichText,
  }) {
    return NoteTemplate(
      id: id ?? this.id,
      name: name ?? this.name,
      title: title ?? this.title,
      content: content ?? this.content,
      category: category ?? this.category,
      description: description ?? this.description,
      tags: tags ?? this.tags,
      isBuiltIn: isBuiltIn ?? this.isBuiltIn,
      isFavorite: isFavorite ?? this.isFavorite,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      richTextContent: richTextContent ?? this.richTextContent,
      isRichText: isRichText ?? this.isRichText,
    );
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'title': title,
      'content': content,
      'category': category,
      'description': description,
      'tags': tags,
      'isBuiltIn': isBuiltIn,
      'isFavorite': isFavorite,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'createdBy': createdBy,
      'richTextContent': richTextContent,
      'isRichText': isRichText,
    };
  }

  /// Create from JSON
  factory NoteTemplate.fromJson(Map<String, dynamic> json) {
    return NoteTemplate(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      category: json['category'] ?? 'Other',
      description: json['description'] ?? '',
      tags: List<String>.from(json['tags'] ?? []),
      isBuiltIn: json['isBuiltIn'] ?? false,
      isFavorite: json['isFavorite'] ?? false,
      createdAt:
          DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt:
          DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
      createdBy: json['createdBy'],
      richTextContent: json['richTextContent'],
      isRichText: json['isRichText'] ?? false,
    );
  }

  Map<String, dynamic> toMap() {
    return toJson();
  }

  factory NoteTemplate.fromMap(Map<String, dynamic> map) {
    return NoteTemplate.fromJson(map);
  }

  /// Create template from existing note (with rich text support)
  factory NoteTemplate.fromNote({
    required Note note,
    required String templateName,
    String? description,
    List<String>? tags,
  }) {
    final now = DateTime.now();

    // Enhanced description for rich text templates
    String templateDescription =
        description ?? 'Template created from note: ${note.title}';
    if (note.isRichText && note.richTextContent != null) {
      try {
        final richContent = note.getRichTextContent();
        if (richContent != null) {
          final stats = richContent.getTextStatistics();
          if (stats['has_formatting'] == true) {
            templateDescription += ' (Rich Text with formatting)';
          }
        }
      } catch (e) {
        // If rich text parsing fails, continue with basic description
      }
    }

    return NoteTemplate(
      id: 'template_${now.millisecondsSinceEpoch}',
      name: templateName,
      title: note.title,
      content: note.content,
      category: note.category,
      description: templateDescription,
      tags: tags ?? note.tags,
      isBuiltIn: false,
      isFavorite: false,
      createdAt: now,
      updatedAt: now,
      createdBy: 'user',
      richTextContent: note.richTextContent,
      isRichText: note.isRichText,
    );
  }

  /// Get formatted creation date
  String get formattedCreatedAt {
    return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
  }

  /// Get template type display
  String get typeDisplay {
    return isBuiltIn ? 'Built-in' : 'Custom';
  }

  /// Check if template can be edited
  bool get canEdit {
    return !isBuiltIn;
  }

  /// Check if template can be deleted
  bool get canDelete {
    return !isBuiltIn;
  }

  /// Get rich text content as RichTextContent object
  RichTextContent? getRichTextContent() {
    if (!isRichText || richTextContent == null) return null;

    try {
      return RichTextContent(
        id: id,
        plainText: content,
        deltaJson: richTextContent!,
        createdAt: createdAt,
        updatedAt: updatedAt,
      );
    } catch (e) {
      return null;
    }
  }

  /// Create note from this template (with rich text support)
  Note createNote({
    required String noteId,
    String? customTitle,
    String? customContent,
    String? customCategory,
    bool isFavorite = false,
    bool isPinned = false,
    List<String>? customTags,
  }) {
    final now = DateTime.now();

    return Note(
      id: noteId,
      title: customTitle ?? title,
      content: customContent ?? content,
      category: customCategory ?? category,
      isFavorite: isFavorite,
      isPinned: isPinned,
      tags: customTags ?? tags,
      richTextContent: richTextContent,
      isRichText: isRichText,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Check if template has any formatting
  bool get hasFormatting {
    return isRichText &&
        richTextContent != null &&
        richTextContent!.contains('"attributes"');
  }

  /// Get template type display with rich text indicator
  String get typeDisplayWithRichText {
    final baseType = isBuiltIn ? 'Built-in' : 'Custom';
    return isRichText ? '$baseType (Rich Text)' : baseType;
  }

  @override
  String toString() {
    return 'NoteTemplate(id: $id, name: $name, category: $category, isBuiltIn: $isBuiltIn, isRichText: $isRichText)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NoteTemplate && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
