import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:image_picker/image_picker.dart';
import '../models/rich_text_content.dart';

/// Comprehensive rich text editor widget with Google Docs-like functionality
class RichTextEditor extends StatefulWidget {
  final RichTextContent? initialContent;
  final Function(RichTextContent)? onContentChanged;
  final bool readOnly;
  final String? placeholder;
  final double? minHeight;
  final double? maxHeight;

  const RichTextEditor({
    super.key,
    this.initialContent,
    this.onContentChanged,
    this.readOnly = false,
    this.placeholder,
    this.minHeight,
    this.maxHeight,
  });

  @override
  State<RichTextEditor> createState() => _RichTextEditorState();
}

class _RichTextEditorState extends State<RichTextEditor> {
  late QuillController _controller;
  late FocusNode _focusNode;
  bool _isToolbarVisible = true;
  bool _isFormatting = false;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _initializeController();
  }

  void _initializeController() {
    if (widget.initialContent != null) {
      _controller = QuillController(
        document: widget.initialContent!.toDocument(),
        selection: const TextSelection.collapsed(offset: 0),
      );
    } else {
      _controller = QuillController.basic();
    }

    _controller.addListener(_onContentChanged);
  }

  void _onContentChanged() {
    if (widget.onContentChanged != null) {
      // If we're in the middle of formatting, delay the content update
      // to ensure the formatting is fully applied
      if (_isFormatting) {
        Future.delayed(const Duration(milliseconds: 50), () {
          if (mounted) {
            _notifyContentChanged();
          }
        });
      } else {
        _notifyContentChanged();
      }
    }
  }

  void _notifyContentChanged() {
    debugPrint('RichTextEditor: _notifyContentChanged called');
    debugPrint(
        'RichTextEditor: Document delta: ${jsonEncode(_controller.document.toDelta().toJson())}');

    final content = RichTextContent.fromDocument(
      id: widget.initialContent?.id ??
          DateTime.now().millisecondsSinceEpoch.toString(),
      document: _controller.document,
      metadata: widget.initialContent?.metadata,
    );

    debugPrint('RichTextEditor: Created content delta: ${content.deltaJson}');
    widget.onContentChanged!(content);
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      children: [
        // Toolbar
        if (_isToolbarVisible && !widget.readOnly) _buildToolbar(theme),

        // Editor
        Expanded(
          child: Container(
            constraints: BoxConstraints(
              minHeight: widget.minHeight ?? 200,
              maxHeight: widget.maxHeight ?? double.infinity,
            ),
            decoration: BoxDecoration(
              border: Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.3),
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: QuillEditor.basic(
                controller: _controller,
                focusNode: _focusNode,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildToolbar(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          // First row - Basic formatting
          _buildToolbarRow([
            _buildFontSizeSelector(),
            const SizedBox(width: 8),
            _buildBoldButton(),
            _buildItalicButton(),
            _buildUnderlineButton(),
            const SizedBox(width: 8),
            _buildTextColorButton(),
            _buildHighlightColorButton(),
            const SizedBox(width: 8),
            _buildAlignmentButtons(),
          ]),

          const SizedBox(height: 4),

          // Second row - Lists and advanced features
          _buildToolbarRow([
            _buildListButtons(),
            const SizedBox(width: 8),
            _buildIndentButtons(),
            const SizedBox(width: 8),
            _buildInsertButtons(),
            const SizedBox(width: 8),
            _buildMoreOptionsButton(),
          ]),
        ],
      ),
    );
  }

  Widget _buildToolbarRow(List<Widget> children) {
    return SizedBox(
      height: 40,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: children,
        ),
      ),
    );
  }

  Widget _buildFontSizeSelector() {
    return PopupMenuButton<double>(
      icon: const Icon(Icons.format_size, size: 18),
      tooltip: 'Font Size',
      iconSize: 32,
      onSelected: (size) {
        _isFormatting = true;
        _controller.formatSelection(Attribute.fromKeyValue('size', size));
        Future.delayed(const Duration(milliseconds: 100), () {
          _isFormatting = false;
        });
      },
      itemBuilder: (context) => RichTextPresets.fontSizes.map((size) {
        return PopupMenuItem(
          value: size,
          child: Text('${size.toInt()}pt'),
        );
      }).toList(),
    );
  }

  Widget _buildBoldButton() {
    return IconButton(
      icon: const Icon(Icons.format_bold, size: 18),
      tooltip: 'Bold',
      iconSize: 32,
      onPressed: () {
        debugPrint('RichTextEditor: Bold button pressed');
        debugPrint(
            'RichTextEditor: Current selection: ${_controller.selection}');
        debugPrint(
            'RichTextEditor: Document before bold: ${jsonEncode(_controller.document.toDelta().toJson())}');

        _isFormatting = true;
        _controller.formatSelection(Attribute.bold);

        debugPrint(
            'RichTextEditor: Document after bold: ${jsonEncode(_controller.document.toDelta().toJson())}');

        Future.delayed(const Duration(milliseconds: 100), () {
          _isFormatting = false;
        });
      },
    );
  }

  Widget _buildItalicButton() {
    return IconButton(
      icon: const Icon(Icons.format_italic, size: 18),
      tooltip: 'Italic',
      iconSize: 32,
      onPressed: () {
        _isFormatting = true;
        _controller.formatSelection(Attribute.italic);
        Future.delayed(const Duration(milliseconds: 100), () {
          _isFormatting = false;
        });
      },
    );
  }

  Widget _buildUnderlineButton() {
    return IconButton(
      icon: const Icon(Icons.format_underlined, size: 18),
      tooltip: 'Underline',
      iconSize: 32,
      onPressed: () {
        _isFormatting = true;
        _controller.formatSelection(Attribute.underline);
        Future.delayed(const Duration(milliseconds: 100), () {
          _isFormatting = false;
        });
      },
    );
  }

  Widget _buildTextColorButton() {
    return IconButton(
      icon: const Icon(Icons.format_color_text, size: 18),
      tooltip: 'Text Color',
      iconSize: 32,
      onPressed: () => _showColorPicker(isBackground: false),
    );
  }

  Widget _buildHighlightColorButton() {
    return IconButton(
      icon: const Icon(Icons.format_color_fill, size: 18),
      tooltip: 'Highlight Color',
      iconSize: 32,
      onPressed: () => _showColorPicker(isBackground: true),
    );
  }

  Widget _buildAlignmentButtons() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: const Icon(Icons.format_align_left, size: 16),
          tooltip: 'Align Left',
          iconSize: 32,
          onPressed: () {
            _isFormatting = true;
            _controller
                .formatSelection(Attribute.fromKeyValue('align', 'left'));
            Future.delayed(const Duration(milliseconds: 100), () {
              _isFormatting = false;
            });
          },
        ),
        IconButton(
          icon: const Icon(Icons.format_align_center, size: 16),
          tooltip: 'Align Center',
          iconSize: 32,
          onPressed: () {
            _isFormatting = true;
            _controller
                .formatSelection(Attribute.fromKeyValue('align', 'center'));
            Future.delayed(const Duration(milliseconds: 100), () {
              _isFormatting = false;
            });
          },
        ),
        IconButton(
          icon: const Icon(Icons.format_align_right, size: 16),
          tooltip: 'Align Right',
          iconSize: 32,
          onPressed: () {
            _isFormatting = true;
            _controller
                .formatSelection(Attribute.fromKeyValue('align', 'right'));
            Future.delayed(const Duration(milliseconds: 100), () {
              _isFormatting = false;
            });
          },
        ),
        IconButton(
          icon: const Icon(Icons.format_align_justify, size: 16),
          tooltip: 'Justify',
          iconSize: 32,
          onPressed: () {
            _isFormatting = true;
            _controller
                .formatSelection(Attribute.fromKeyValue('align', 'justify'));
            Future.delayed(const Duration(milliseconds: 100), () {
              _isFormatting = false;
            });
          },
        ),
      ],
    );
  }

  Widget _buildListButtons() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: const Icon(Icons.format_list_bulleted, size: 18),
          tooltip: 'Bullet List',
          iconSize: 32,
          onPressed: () {
            _isFormatting = true;
            _controller
                .formatSelection(Attribute.fromKeyValue('list', 'bullet'));
            Future.delayed(const Duration(milliseconds: 100), () {
              _isFormatting = false;
            });
          },
        ),
        IconButton(
          icon: const Icon(Icons.format_list_numbered, size: 18),
          tooltip: 'Numbered List',
          iconSize: 32,
          onPressed: () {
            _isFormatting = true;
            _controller
                .formatSelection(Attribute.fromKeyValue('list', 'ordered'));
            Future.delayed(const Duration(milliseconds: 100), () {
              _isFormatting = false;
            });
          },
        ),
        IconButton(
          icon: const Icon(Icons.checklist, size: 18),
          tooltip: 'Checklist',
          iconSize: 32,
          onPressed: () {
            _isFormatting = true;
            _controller
                .formatSelection(Attribute.fromKeyValue('list', 'checked'));
            Future.delayed(const Duration(milliseconds: 100), () {
              _isFormatting = false;
            });
          },
        ),
      ],
    );
  }

  Widget _buildIndentButtons() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: const Icon(Icons.format_indent_decrease, size: 18),
          tooltip: 'Decrease Indent',
          iconSize: 32,
          onPressed: () {
            _isFormatting = true;
            _controller.formatSelection(Attribute.fromKeyValue('indent', -1));
            Future.delayed(const Duration(milliseconds: 100), () {
              _isFormatting = false;
            });
          },
        ),
        IconButton(
          icon: const Icon(Icons.format_indent_increase, size: 18),
          tooltip: 'Increase Indent',
          iconSize: 32,
          onPressed: () {
            _isFormatting = true;
            _controller.formatSelection(Attribute.fromKeyValue('indent', 1));
            Future.delayed(const Duration(milliseconds: 100), () {
              _isFormatting = false;
            });
          },
        ),
      ],
    );
  }

  Widget _buildInsertButtons() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: const Icon(Icons.image, size: 18),
          tooltip: 'Insert Image',
          iconSize: 32,
          onPressed: _insertImage,
        ),
        IconButton(
          icon: const Icon(Icons.link, size: 18),
          tooltip: 'Insert Link',
          iconSize: 32,
          onPressed: _insertLink,
        ),
      ],
    );
  }

  Widget _buildMoreOptionsButton() {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.more_horiz, size: 18),
      tooltip: 'More Options',
      iconSize: 32,
      onSelected: _handleMoreOption,
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'line_spacing',
          child: Row(
            children: [
              Icon(Icons.format_line_spacing, size: 18),
              SizedBox(width: 8),
              Text('Line Spacing'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'clear_formatting',
          child: Row(
            children: [
              Icon(Icons.format_clear, size: 18),
              SizedBox(width: 8),
              Text('Clear Formatting'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'toggle_toolbar',
          child: Row(
            children: [
              Icon(Icons.keyboard_hide, size: 18),
              SizedBox(width: 8),
              Text('Hide Toolbar'),
            ],
          ),
        ),
      ],
    );
  }

  void _showColorPicker({required bool isBackground}) {
    Color currentColor = Colors.black;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title:
            Text(isBackground ? 'Choose Highlight Color' : 'Choose Text Color'),
        content: SingleChildScrollView(
          child: ColorPicker(
            pickerColor: currentColor,
            onColorChanged: (color) => currentColor = color,
            pickerAreaHeightPercent: 0.8,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              _isFormatting = true;
              final colorValue = (currentColor.a * 255).round() << 24 |
                  (currentColor.r * 255).round() << 16 |
                  (currentColor.g * 255).round() << 8 |
                  (currentColor.b * 255).round();
              final colorHex =
                  '#${colorValue.toRadixString(16).padLeft(8, '0').substring(2)}';
              if (isBackground) {
                _controller.formatSelection(
                  Attribute.fromKeyValue('background', colorHex),
                );
              } else {
                _controller.formatSelection(
                  Attribute.fromKeyValue('color', colorHex),
                );
              }
              Future.delayed(const Duration(milliseconds: 100), () {
                _isFormatting = false;
              });
              Navigator.pop(context);
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  void _insertImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);

    if (pickedFile != null) {
      final file = File(pickedFile.path);
      final index = _controller.selection.baseOffset;

      // Insert image embed
      _controller.document.insert(index, BlockEmbed.image(file.path));
      _controller.updateSelection(
        TextSelection.collapsed(offset: index + 1),
        ChangeSource.local,
      );
    }
  }

  void _insertLink() {
    final textController = TextEditingController();
    final urlController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Insert Link'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: textController,
              decoration: const InputDecoration(
                labelText: 'Link Text',
                hintText: 'Enter link text',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: urlController,
              decoration: const InputDecoration(
                labelText: 'URL',
                hintText: 'https://example.com',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final text = textController.text.trim();
              final url = urlController.text.trim();

              if (text.isNotEmpty && url.isNotEmpty) {
                final index = _controller.selection.baseOffset;
                _controller.document.insert(index, text);
                _controller.formatText(
                  index,
                  text.length,
                  Attribute.fromKeyValue('link', url),
                );
              }
              Navigator.pop(context);
            },
            child: const Text('Insert'),
          ),
        ],
      ),
    );
  }

  void _handleMoreOption(String option) {
    switch (option) {
      case 'line_spacing':
        _showLineSpacingDialog();
        break;
      case 'clear_formatting':
        _clearFormatting();
        break;
      case 'toggle_toolbar':
        setState(() {
          _isToolbarVisible = !_isToolbarVisible;
        });
        break;
    }
  }

  void _showLineSpacingDialog() {
    double currentSpacing = 1.0;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Line Spacing'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Spacing: ${currentSpacing.toStringAsFixed(1)}'),
              Slider(
                value: currentSpacing,
                min: 0.5,
                max: 3.0,
                divisions: 25,
                onChanged: (value) {
                  setState(() {
                    currentSpacing = value;
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                _isFormatting = true;
                _controller.formatSelection(
                  Attribute.fromKeyValue('lineHeight', currentSpacing),
                );
                Future.delayed(const Duration(milliseconds: 100), () {
                  _isFormatting = false;
                });
                Navigator.pop(context);
              },
              child: const Text('Apply'),
            ),
          ],
        ),
      ),
    );
  }

  void _clearFormatting() {
    _isFormatting = true;
    final selection = _controller.selection;
    if (selection.isValid) {
      // Clear common formatting attributes
      _controller.formatSelection(Attribute.fromKeyValue('bold', null));
      _controller.formatSelection(Attribute.fromKeyValue('italic', null));
      _controller.formatSelection(Attribute.fromKeyValue('underline', null));
      _controller.formatSelection(Attribute.fromKeyValue('color', null));
      _controller.formatSelection(Attribute.fromKeyValue('background', null));
      _controller.formatSelection(Attribute.fromKeyValue('size', null));
    }
    Future.delayed(const Duration(milliseconds: 100), () {
      _isFormatting = false;
    });
  }

  /// Get current content as RichTextContent
  RichTextContent getCurrentContent() {
    return RichTextContent.fromDocument(
      id: widget.initialContent?.id ??
          DateTime.now().millisecondsSinceEpoch.toString(),
      document: _controller.document,
      metadata: widget.initialContent?.metadata ?? {},
    );
  }

  /// Set content from RichTextContent
  void setContent(RichTextContent content) {
    _controller.document = content.toDocument();
    _controller.updateSelection(
      const TextSelection.collapsed(offset: 0),
      ChangeSource.local,
    );
  }

  /// Toggle toolbar visibility
  void toggleToolbar() {
    setState(() {
      _isToolbarVisible = !_isToolbarVisible;
    });
  }
}
