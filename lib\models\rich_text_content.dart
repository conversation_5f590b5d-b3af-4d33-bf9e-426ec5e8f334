import 'dart:convert';
import 'package:flutter_quill/flutter_quill.dart';

/// Rich text content model for storing formatted note content
class RichTextContent {
  final String id;
  final String plainText;
  final String deltaJson; // Quill Delta format
  final Map<String, dynamic> metadata;
  final DateTime createdAt;
  final DateTime updatedAt;

  RichTextContent({
    required this.id,
    required this.plainText,
    required this.deltaJson,
    this.metadata = const {},
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create from Quill Document
  factory RichTextContent.fromDocument({
    required String id,
    required Document document,
    Map<String, dynamic>? metadata,
  }) {
    final now = DateTime.now();
    return RichTextContent(
      id: id,
      plainText: document.toPlainText(),
      deltaJson: jsonEncode(document.toDelta().toJson()),
      metadata: metadata ?? {},
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Create from plain text (for backward compatibility)
  factory RichTextContent.fromPlainText({
    required String id,
    required String text,
    Map<String, dynamic>? metadata,
  }) {
    final document = Document()..insert(0, text);
    return RichTextContent.fromDocument(
      id: id,
      document: document,
      metadata: metadata,
    );
  }

  /// Convert to Quill Document
  Document toDocument() {
    try {
      final deltaJson = jsonDecode(this.deltaJson);
      final delta = Delta.fromJson(deltaJson);
      return Document.fromDelta(delta);
    } catch (e) {
      // Fallback to plain text if delta parsing fails
      final document = Document();
      document.insert(0, plainText);
      return document;
    }
  }

  /// Get formatted text statistics
  Map<String, dynamic> getTextStatistics() {
    final document = toDocument();
    final text = document.toPlainText();
    
    return {
      'character_count': text.length,
      'word_count': text.split(RegExp(r'\s+')).where((word) => word.isNotEmpty).length,
      'paragraph_count': text.split('\n').where((line) => line.trim().isNotEmpty).length,
      'has_formatting': deltaJson.contains('"attributes"'),
      'has_lists': deltaJson.contains('"list"'),
      'has_images': deltaJson.contains('"image"'),
      'has_links': deltaJson.contains('"link"'),
    };
  }

  /// Extract all text styles used
  List<String> getUsedStyles() {
    final styles = <String>[];
    try {
      final deltaJson = jsonDecode(this.deltaJson);
      final operations = deltaJson['ops'] as List?;
      
      if (operations != null) {
        for (final op in operations) {
          final attributes = op['attributes'] as Map<String, dynamic>?;
          if (attributes != null) {
            styles.addAll(attributes.keys);
          }
        }
      }
    } catch (e) {
      // Ignore parsing errors
    }
    
    return styles.toSet().toList();
  }

  /// Create a copy with updated content
  RichTextContent copyWith({
    String? id,
    String? plainText,
    String? deltaJson,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RichTextContent(
      id: id ?? this.id,
      plainText: plainText ?? this.plainText,
      deltaJson: deltaJson ?? this.deltaJson,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// Convert to map for storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'plain_text': plainText,
      'delta_json': deltaJson,
      'metadata': jsonEncode(metadata),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create from map
  factory RichTextContent.fromMap(Map<String, dynamic> map) {
    return RichTextContent(
      id: map['id'],
      plainText: map['plain_text'] ?? '',
      deltaJson: map['delta_json'] ?? '{"ops":[{"insert":"\\n"}]}',
      metadata: map['metadata'] != null 
          ? jsonDecode(map['metadata']) 
          : {},
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  @override
  String toString() {
    return 'RichTextContent(id: $id, plainText: ${plainText.substring(0, plainText.length > 50 ? 50 : plainText.length)}...)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RichTextContent &&
        other.id == id &&
        other.deltaJson == deltaJson;
  }

  @override
  int get hashCode {
    return id.hashCode ^ deltaJson.hashCode;
  }
}

/// Rich text formatting presets
class RichTextPresets {
  static const Map<String, Map<String, dynamic>> headingStyles = {
    'heading1': {
      'size': 24.0,
      'bold': true,
      'color': '#1976D2',
    },
    'heading2': {
      'size': 20.0,
      'bold': true,
      'color': '#424242',
    },
    'heading3': {
      'size': 18.0,
      'bold': true,
      'color': '#616161',
    },
    'subtitle': {
      'size': 16.0,
      'italic': true,
      'color': '#757575',
    },
    'body': {
      'size': 14.0,
      'color': '#212121',
    },
    'caption': {
      'size': 12.0,
      'color': '#9E9E9E',
    },
  };

  static const List<double> fontSizes = [
    8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72
  ];

  static const List<String> fontFamilies = [
    'Poppins',
    'Arial',
    'Times New Roman',
    'Courier New',
    'Georgia',
    'Verdana',
    'Comic Sans MS',
  ];
}
