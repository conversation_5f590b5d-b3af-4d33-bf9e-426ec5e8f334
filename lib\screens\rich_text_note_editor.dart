import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/note.dart';
import '../models/rich_text_content.dart';
import '../providers/note_provider.dart';
import '../widgets/rich_text_editor.dart';
// import '../widgets/voice_note_dialog.dart'; // Temporarily disabled

/// Rich Text Note Editor with Google Docs-like functionality
class RichTextNoteEditor extends StatefulWidget {
  final Note? note;
  final String? initialCategory;
  final String? initialTitle;
  final String? initialContent;

  const RichTextNoteEditor({
    super.key,
    this.note,
    this.initialCategory,
    this.initialTitle,
    this.initialContent,
  });

  @override
  State<RichTextNoteEditor> createState() => _RichTextNoteEditorState();
}

class _RichTextNoteEditorState extends State<RichTextNoteEditor> {
  late TextEditingController _titleController;
  late String _selectedCategory;
  bool _isFavorite = false;
  bool _isPinned = false;
  bool _hasUnsavedChanges = false;
  bool _isSaving = false;

  RichTextContent? _currentRichTextContent;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  final List<String> _categories = [
    'Work',
    'Personal',
    'Ideas',
    'Meeting',
    'Study',
    'Project',
    'Quick Note',
  ];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _setupInitialContent();
  }

  void _initializeControllers() {
    _titleController = TextEditingController(
      text: widget.note?.title ?? widget.initialTitle ?? '',
    );
    _selectedCategory =
        widget.note?.category ?? widget.initialCategory ?? _categories.first;
    _isFavorite = widget.note?.isFavorite ?? false;
    _isPinned = widget.note?.isPinned ?? false;

    _titleController.addListener(_onContentChanged);
  }

  void _setupInitialContent() {
    if (widget.note != null) {
      // Load existing note content
      debugPrint('RichTextEditor: Loading note with ID: ${widget.note!.id}');
      debugPrint('RichTextEditor: Note isRichText: ${widget.note!.isRichText}');
      debugPrint(
          'RichTextEditor: Note richTextContent length: ${widget.note!.richTextContent?.length ?? 0}');
      if (widget.note!.richTextContent != null) {
        debugPrint(
            'RichTextEditor: Rich text content preview: ${widget.note!.richTextContent!.substring(0, widget.note!.richTextContent!.length > 200 ? 200 : widget.note!.richTextContent!.length)}...');
      }

      final richContent = widget.note!.getRichTextContent();
      if (richContent != null) {
        debugPrint('RichTextEditor: Successfully loaded rich text content');
        debugPrint(
            'RichTextEditor: Rich content has formatting: ${richContent.getTextStatistics()['has_formatting']}');
        _currentRichTextContent = richContent;
      } else {
        debugPrint(
            'RichTextEditor: No rich text content found, converting plain text');
        // Convert plain text to rich text
        _currentRichTextContent = RichTextContent.fromPlainText(
          id: widget.note!.id,
          text: widget.note!.content,
        );
      }
    } else if (widget.initialContent != null) {
      // Create new rich text content from initial content
      _currentRichTextContent = RichTextContent.fromPlainText(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        text: widget.initialContent!,
      );
    } else {
      // Create empty rich text content
      _currentRichTextContent = RichTextContent.fromPlainText(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        text: '',
      );
    }
  }

  void _onContentChanged() {
    if (!_hasUnsavedChanges) {
      setState(() {
        _hasUnsavedChanges = true;
      });
    }
  }

  void _onRichTextChanged(RichTextContent content) {
    _currentRichTextContent = content;
    _onContentChanged();
  }

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      key: _scaffoldKey,
      appBar: _buildAppBar(theme),
      body: _buildBody(theme),
      bottomNavigationBar: _buildBottomBar(theme),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeData theme) {
    return AppBar(
      title: Text(widget.note != null ? 'Edit Note' : 'Create Note'),
      backgroundColor: theme.colorScheme.surface,
      foregroundColor: theme.colorScheme.onSurface,
      elevation: 0,
      actions: [
        // Pin toggle
        IconButton(
          icon: Icon(
            _isPinned ? Icons.push_pin : Icons.push_pin_outlined,
            color: _isPinned ? theme.colorScheme.primary : null,
          ),
          onPressed: () {
            setState(() {
              _isPinned = !_isPinned;
              _onContentChanged();
            });
          },
          tooltip: _isPinned ? 'Unpin note' : 'Pin note',
        ),

        // Favorite toggle
        IconButton(
          icon: Icon(
            _isFavorite ? Icons.favorite : Icons.favorite_border,
            color: _isFavorite ? Colors.red : null,
          ),
          onPressed: () {
            setState(() {
              _isFavorite = !_isFavorite;
              _onContentChanged();
            });
          },
          tooltip: _isFavorite ? 'Remove from favorites' : 'Add to favorites',
        ),

        // Voice note button
        IconButton(
          icon: const Icon(Icons.mic),
          onPressed: _startVoiceNote,
          tooltip: 'Voice Note',
        ),

        // Save button
        IconButton(
          icon: _isSaving
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.save),
          onPressed: _isSaving ? null : _saveNote,
          tooltip: 'Save Note',
        ),
      ],
    );
  }

  Widget _buildBody(ThemeData theme) {
    return Column(
      children: [
        // Note metadata section
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            border: Border(
              bottom: BorderSide(
                color: theme.colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
          ),
          child: Column(
            children: [
              // Title field
              TextFormField(
                controller: _titleController,
                decoration: InputDecoration(
                  labelText: 'Note Title',
                  hintText: 'Enter note title...',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  filled: true,
                  fillColor: theme.colorScheme.surfaceContainerHighest
                      .withValues(alpha: 0.3),
                ),
                style: theme.textTheme.titleLarge,
                textInputAction: TextInputAction.next,
              ),

              const SizedBox(height: 16),

              // Category dropdown
              DropdownButtonFormField<String>(
                value: _selectedCategory,
                decoration: InputDecoration(
                  labelText: 'Category',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  filled: true,
                  fillColor: theme.colorScheme.surfaceContainerHighest
                      .withValues(alpha: 0.3),
                ),
                items: _categories.map((category) {
                  return DropdownMenuItem(
                    value: category,
                    child: Text(category),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedCategory = value!;
                    _onContentChanged();
                  });
                },
              ),
            ],
          ),
        ),

        // Rich text editor
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: RichTextEditor(
              initialContent: _currentRichTextContent,
              onContentChanged: _onRichTextChanged,
              placeholder: 'Start writing your note...',
              minHeight: 200,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomBar(ThemeData theme) {
    final wordCount =
        _currentRichTextContent?.getTextStatistics()['word_count'] ?? 0;
    final charCount =
        _currentRichTextContent?.getTextStatistics()['character_count'] ?? 0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          // Statistics
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$wordCount words • $charCount characters',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
                if (_currentRichTextContent
                        ?.getTextStatistics()['has_formatting'] ==
                    true)
                  Text(
                    'Rich text formatting applied',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
              ],
            ),
          ),

          // Action buttons
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),

          const SizedBox(width: 8),

          ElevatedButton(
            onPressed: _isSaving ? null : _saveNote,
            child: _isSaving
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Text(widget.note != null ? 'Update' : 'Save'),
          ),
        ],
      ),
    );
  }

  Future<void> _saveNote() async {
    if (_titleController.text.trim().isEmpty) {
      _showErrorSnackBar('Please enter a title for the note');
      return;
    }

    if (_currentRichTextContent == null) {
      _showErrorSnackBar('Note content is required');
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final noteProvider = Provider.of<NoteProvider>(context, listen: false);

      if (widget.note != null) {
        // Update existing note with rich text content
        debugPrint('RichTextEditor: Saving rich text content...');
        debugPrint(
            'RichTextEditor: Delta JSON length: ${_currentRichTextContent!.deltaJson.length}');
        debugPrint(
            'RichTextEditor: Has formatting: ${_currentRichTextContent!.getTextStatistics()['has_formatting']}');
        debugPrint(
            'RichTextEditor: Delta JSON preview: ${_currentRichTextContent!.deltaJson.substring(0, _currentRichTextContent!.deltaJson.length > 200 ? 200 : _currentRichTextContent!.deltaJson.length)}...');

        final updatedNote = widget.note!.copyWith(
          title: _titleController.text.trim(),
          category: _selectedCategory,
          isFavorite: _isFavorite,
          isPinned: _isPinned,
          content: _currentRichTextContent!.plainText,
          richTextContent: _currentRichTextContent!.deltaJson,
          isRichText: true,
          updatedAt: DateTime.now(),
        );

        debugPrint(
            'RichTextEditor: Updated note rich text content: ${updatedNote.richTextContent?.substring(0, updatedNote.richTextContent!.length > 200 ? 200 : updatedNote.richTextContent!.length)}...');

        final result = await noteProvider.updateNote(updatedNote);
        if (result != null) {
          debugPrint('RichTextEditor: Note saved successfully');
          debugPrint(
              'RichTextEditor: Saved note rich text content: ${result.richTextContent?.substring(0, result.richTextContent!.length > 200 ? 200 : result.richTextContent!.length)}...');
          setState(() {
            _hasUnsavedChanges = false;
          });
          _showSuccessSnackBar('Note updated successfully');
          if (mounted) Navigator.of(context).pop(result);
        } else {
          _showErrorSnackBar('Failed to update note');
        }
      } else {
        // Create new note first with plain text content
        final result = await noteProvider.createNote(
          title: _titleController.text.trim(),
          content: _currentRichTextContent!.plainText,
          category: _selectedCategory,
          isFavorite: _isFavorite,
          isPinned: _isPinned,
          tags: const [],
        );

        if (result != null) {
          // Update the created note with rich text content
          final updatedNote = result.copyWith(
            richTextContent: _currentRichTextContent!.deltaJson,
            isRichText: true,
            updatedAt: DateTime.now(),
          );

          final finalResult = await noteProvider.updateNote(updatedNote);

          if (finalResult != null) {
            setState(() {
              _hasUnsavedChanges = false;
            });
            _showSuccessSnackBar('Note created successfully');
            if (mounted) Navigator.of(context).pop(finalResult);
          } else {
            _showErrorSnackBar('Failed to save rich text content');
          }
        } else {
          _showErrorSnackBar('Failed to create note');
        }
      }
    } catch (e) {
      _showErrorSnackBar('Error saving note: ${e.toString()}');
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  void _showSuccessSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  void _startVoiceNote() {
    // Temporarily disabled - voice note dialog needs to be implemented
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Voice notes feature coming soon!'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}
