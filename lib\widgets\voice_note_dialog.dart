import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';
import '../services/voice_notes_service.dart';
import '../models/voice_note.dart';

/// Voice Note Recording Dialog
class VoiceNoteDialog extends StatefulWidget {
  final Function(String title, String? transcription, String audioPath)? onSave;
  final VoiceNote? existingVoiceNote;

  const VoiceNoteDialog({
    super.key,
    this.onSave,
    this.existingVoiceNote,
  });

  @override
  State<VoiceNoteDialog> createState() => _VoiceNoteDialogState();
}

class _VoiceNoteDialogState extends State<VoiceNoteDialog>
    with TickerProviderStateMixin {
  final VoiceNotesService _voiceService = VoiceNotesService();
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _transcriptionController =
      TextEditingController();

  bool _isInitialized = false;
  bool _isRecording = false;
  bool _isListening = false;
  bool _isPlaying = false;
  bool _hasRecording = false;
  String? _recordingPath;
  Duration _recordingDuration = Duration.zero;
  Duration _playbackPosition = Duration.zero;
  Duration? _totalDuration;

  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  StreamSubscription<PlayerState>? _playerStateSubscription;
  StreamSubscription<Duration>? _positionSubscription;
  StreamSubscription<Duration?>? _durationSubscription;

  Timer? _recordingTimer;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeVoiceService();
    _setupAudioListeners();

    if (widget.existingVoiceNote != null) {
      _loadExistingVoiceNote();
    }
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
  }

  Future<void> _initializeVoiceService() async {
    final initialized = await _voiceService.initialize();
    setState(() {
      _isInitialized = initialized;
    });

    if (!initialized) {
      _showErrorSnackBar('Failed to initialize voice recording');
    }
  }

  void _setupAudioListeners() {
    _playerStateSubscription = _voiceService.playerStateStream.listen((state) {
      setState(() {
        _isPlaying = state.playing;
      });
    });

    _positionSubscription = _voiceService.positionStream.listen((position) {
      setState(() {
        _playbackPosition = position;
      });
    });

    _durationSubscription = _voiceService.durationStream.listen((duration) {
      setState(() {
        _totalDuration = duration;
      });
    });
  }

  void _loadExistingVoiceNote() {
    final voiceNote = widget.existingVoiceNote!;
    _titleController.text = voiceNote.title;
    _transcriptionController.text = voiceNote.transcription ?? '';
    _recordingPath = voiceNote.audioFilePath;
    _totalDuration = voiceNote.duration;
    _hasRecording = File(voiceNote.audioFilePath).existsSync();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxHeight: 600),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(theme),
            const SizedBox(height: 24),
            _buildRecordingSection(theme),
            const SizedBox(height: 24),
            _buildTranscriptionSection(theme),
            const SizedBox(height: 24),
            _buildTitleSection(theme),
            const SizedBox(height: 24),
            _buildActionButtons(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Row(
      children: [
        Icon(
          Icons.mic,
          color: theme.colorScheme.primary,
          size: 28,
        ),
        const SizedBox(width: 12),
        Text(
          widget.existingVoiceNote != null ? 'Edit Voice Note' : 'Voice Note',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.close),
        ),
      ],
    );
  }

  Widget _buildRecordingSection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _isRecording
              ? theme.colorScheme.error.withValues(alpha: 0.5)
              : theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          // Recording/Playback Controls
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildRecordButton(theme),
              if (_hasRecording) _buildPlayButton(theme),
              if (_hasRecording) _buildDeleteButton(theme),
            ],
          ),

          const SizedBox(height: 16),

          // Duration and Progress
          if (_isRecording || _hasRecording) _buildProgressSection(theme),

          // Speech-to-Text Button
          if (!_isRecording) _buildSpeechToTextButton(theme),
        ],
      ),
    );
  }

  Widget _buildRecordButton(ThemeData theme) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _isRecording ? _pulseAnimation.value : 1.0,
          child: GestureDetector(
            onTap: _isInitialized ? _toggleRecording : null,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _isRecording
                    ? theme.colorScheme.error
                    : theme.colorScheme.primary,
                boxShadow: [
                  BoxShadow(
                    color: (_isRecording
                            ? theme.colorScheme.error
                            : theme.colorScheme.primary)
                        .withValues(alpha: 0.3),
                    blurRadius: 12,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: Icon(
                _isRecording ? Icons.stop : Icons.fiber_manual_record,
                color: Colors.white,
                size: 32,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPlayButton(ThemeData theme) {
    return GestureDetector(
      onTap: _togglePlayback,
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: theme.colorScheme.secondary,
        ),
        child: Icon(
          _isPlaying ? Icons.pause : Icons.play_arrow,
          color: Colors.white,
          size: 28,
        ),
      ),
    );
  }

  Widget _buildDeleteButton(ThemeData theme) {
    return GestureDetector(
      onTap: _deleteRecording,
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: theme.colorScheme.error.withValues(alpha: 0.1),
          border: Border.all(color: theme.colorScheme.error),
        ),
        child: Icon(
          Icons.delete_outline,
          color: theme.colorScheme.error,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildProgressSection(ThemeData theme) {
    final duration = _isRecording ? _recordingDuration : _totalDuration;
    final position = _isRecording ? _recordingDuration : _playbackPosition;

    return Column(
      children: [
        if (duration != null && duration.inSeconds > 0)
          LinearProgressIndicator(
            value: position.inMilliseconds / duration.inMilliseconds,
            backgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(
              _isRecording
                  ? theme.colorScheme.error
                  : theme.colorScheme.primary,
            ),
          ),
        const SizedBox(height: 8),
        Text(
          _formatDuration(position),
          style: theme.textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w500,
            color: _isRecording ? theme.colorScheme.error : null,
          ),
        ),
      ],
    );
  }

  Widget _buildSpeechToTextButton(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(top: 12),
      child: OutlinedButton.icon(
        onPressed: _isInitialized ? _toggleSpeechToText : null,
        icon: Icon(
          _isListening ? Icons.mic_off : Icons.mic,
          color: _isListening ? theme.colorScheme.error : null,
        ),
        label: Text(_isListening ? 'Stop Listening' : 'Speech to Text'),
        style: OutlinedButton.styleFrom(
          foregroundColor: _isListening ? theme.colorScheme.error : null,
          side: BorderSide(
            color: _isListening
                ? theme.colorScheme.error
                : theme.colorScheme.outline,
          ),
        ),
      ),
    );
  }

  Widget _buildTranscriptionSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Transcription',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _transcriptionController,
          maxLines: 4,
          decoration: InputDecoration(
            hintText: 'Transcription will appear here...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            filled: true,
            fillColor: theme.colorScheme.surface,
          ),
        ),
      ],
    );
  }

  Widget _buildTitleSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Title',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _titleController,
          decoration: InputDecoration(
            hintText: 'Enter voice note title...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            filled: true,
            fillColor: theme.colorScheme.surface,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(ThemeData theme) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _canSave ? _saveVoiceNote : null,
            child: const Text('Save'),
          ),
        ),
      ],
    );
  }

  // Methods
  Future<void> _toggleRecording() async {
    if (_isRecording) {
      await _stopRecording();
    } else {
      await _startRecording();
    }
  }

  Future<void> _startRecording() async {
    final success = await _voiceService.startRecording();
    if (success) {
      setState(() {
        _isRecording = true;
        _recordingDuration = Duration.zero;
      });

      _pulseController.repeat(reverse: true);
      _startRecordingTimer();
    } else {
      _showErrorSnackBar('Failed to start recording');
    }
  }

  Future<void> _stopRecording() async {
    final path = await _voiceService.stopRecording();
    if (path != null) {
      setState(() {
        _isRecording = false;
        _hasRecording = true;
        _recordingPath = path;
      });

      _pulseController.stop();
      _recordingTimer?.cancel();
    } else {
      _showErrorSnackBar('Failed to stop recording');
    }
  }

  void _startRecordingTimer() {
    _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _recordingDuration = Duration(seconds: timer.tick);
      });
    });
  }

  Future<void> _togglePlayback() async {
    if (_recordingPath == null) return;

    if (_isPlaying) {
      await _voiceService.stopPlayback();
    } else {
      final success = await _voiceService.playRecording(_recordingPath!);
      if (!success) {
        _showErrorSnackBar('Failed to play recording');
      }
    }
  }

  Future<void> _toggleSpeechToText() async {
    if (_isListening) {
      await _voiceService.stopListening();
      setState(() {
        _isListening = false;
      });
    } else {
      final success = await _voiceService.startListening(
        onResult: (text) {
          setState(() {
            _transcriptionController.text = text;
            _isListening = false;
          });
        },
        onPartialResult: (text) {
          setState(() {
            _transcriptionController.text = text;
          });
        },
      );

      if (success) {
        setState(() {
          _isListening = true;
        });
      } else {
        _showErrorSnackBar('Failed to start speech recognition');
      }
    }
  }

  Future<void> _deleteRecording() async {
    final confirmed = await _showDeleteConfirmation();
    if (confirmed && _recordingPath != null) {
      await _voiceService.deleteRecording(_recordingPath!);
      setState(() {
        _hasRecording = false;
        _recordingPath = null;
        _totalDuration = null;
        _playbackPosition = Duration.zero;
      });
    }
  }

  Future<bool> _showDeleteConfirmation() async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Delete Recording'),
            content:
                const Text('Are you sure you want to delete this recording?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('Delete'),
              ),
            ],
          ),
        ) ??
        false;
  }

  Future<void> _saveVoiceNote() async {
    if (!_canSave) return;

    final title = _titleController.text.trim();
    final transcription = _transcriptionController.text.trim();

    if (_recordingPath != null) {
      widget.onSave?.call(
          title, transcription.isEmpty ? null : transcription, _recordingPath!);
      Navigator.pop(context, true);
    }
  }

  bool get _canSave {
    return _titleController.text.trim().isNotEmpty && _hasRecording;
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
      ),
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _titleController.dispose();
    _transcriptionController.dispose();
    _playerStateSubscription?.cancel();
    _positionSubscription?.cancel();
    _durationSubscription?.cancel();
    _recordingTimer?.cancel();
    super.dispose();
  }
}
